FROM python:3.10-slim

# <PERSON><PERSON><PERSON> các gói hệ thống cần thiết
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Tạo thư mục app
WORKDIR /app

# Copy source code
COPY ./app /app/app
COPY requirements.txt .

# C<PERSON>i các gói Python
RUN pip install --upgrade pip && pip install -r requirements.txt

# Expose port
EXPOSE 8000

# Chạy app
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
