from fastapi import FastAP<PERSON>, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from faster_whisper import WhisperModel
from tempfile import NamedTemporaryFile
from app.extractor import extract_info_from_text
from pydantic import BaseModel
import json
import requests

app = FastAPI()

# Cho phép CORS (giới hạn origin trong production)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Giới hạn cụ thể trong production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request bodies
class Account(BaseModel):
    username: str
    password: str


# Load mô hình Faster-Whisper
whisper_model = WhisperModel("medium", compute_type="int8", device="cpu")

@app.post("/transcribe/")
async def transcribe_and_extract(file: UploadFile = File(...)):
    try:
        # Ghi file tạm, sẽ tự xóa sau khi đóng
        with NamedTemporaryFile(suffix=".webm", delete=True) as temp_audio:
            temp_audio.write(await file.read())
            temp_audio.flush()

            # Chuyển giọng nói thành văn bản
            segments, info = whisper_model.transcribe(temp_audio.name, beam_size=5, language="vi", vad_filter=True)
            text_result = " ".join([segment.text.strip() for segment in segments])

        # Trích xuất thông tin từ văn bản
        extracted_json = extract_info_from_text(text_result)

        return {
            "language": info.language,
            "transcription": text_result.strip(),
            "extracted": extracted_json,
        }

    except Exception as e:
        return {
            "error": str(e)
        }

@app.post("/signin")
async def signin(account: Account):
    teable_url = "https://app.teable.io/api/table/tblv9Ou1thzbETynKn1/record"
    headers = {
        "Authorization": "Bearer teable_accT1cTLbgDxAw73HQa_xnRuWiEDLat6qqpUDsL4QEzwnKwnkU9ErG7zgJKJswg=",
        "Accept": "application/json"
    }
    params = {
        "fieldKeyType": "dbFieldName",
        "filter": json.dumps({
            "conjunction": "and",
            "filterSet": [
                {"fieldId": "username", "operator": "is", "value": account.username},
                {"fieldId": "password", "operator": "is", "value": account.password}
            ]
        })
    }

    response = requests.get(teable_url, params=params, headers=headers)
    data = response.json()

    if data and data.get("records"):
        return {"status": "success", "accessToken": "teable_accT1cTLbgDxAw73HQa_xnRuWiEDLat6qqpUDsL4QEzwnKwnkU9ErG7zgJKJswg="}

    else:
        return {"status": "error", "message": "Invalid username or password"}

@app.post("/signup")
async def signup(account: Account):
    teable_url = "https://app.teable.io/api/table/tblv9Ou1thzbETynKn1/record"
    headers = {
        "Authorization": "Bearer teable_accT1cTLbgDxAw73HQa_xnRuWiEDLat6qqpUDsL4QEzwnKwnkU9ErG7zgJKJswg=",
        "Accept": "application/json",
        "content-type": "application/json"
    }

    # Check if account already exists
    params_check = {
        "fieldKeyType": "dbFieldName",
        "filter": json.dumps({"conjunction":"and","filterSet":[{"fieldId":"username","operator":"is","value":account.username}]})
    }
    response_check = requests.get(teable_url, params=params_check, headers=headers)
    data_check = response_check.json()

    if data_check and data_check.get("records"):
        return {"status": "error", "message": "Account with this username already exists"}
    else:
        # Create new record (signup)
        payload_create_account = json.dumps([
    {
        "fields": {
            "username": account.username,
            "password": account.password
        }
    }
])
        response_create_account = requests.post(teable_url, data=payload_create_account, headers=headers)

        if response_create_account.status_code == 201:
            # Create new space
            space_url = "https://app.teable.io/api/space"
            space_payload = json.dumps({"name": f"{account.username}_space"})
            response_create_space = requests.post(space_url, data=space_payload, headers=headers)
            space_data = response_create_space.json()

            if response_create_space.status_code == 201 and space_data.get("id"):
                space_id = space_data["id"]
                # Create new base
                base_url = "https://app.teable.io/api/base"
                base_payload = json.dumps({"spaceId": space_id, "name": f"{account.username}_base", "icon": "📊"})
                response_create_base = requests.post(base_url, data=base_payload, headers=headers)

                if response_create_base.status_code == 201:
                    return {"status": "success", "message": "Account and space created successfully"}
                else:
                    return {"status": "error", "message": "Account created, but failed to create base."}
            else:
                return {"status": "error", "message": "Account created, but failed to create space."}
        else:
            return {"status": "error", "message": "Failed to create account in Teable."}

